"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import {
  WishlistItem,
  WishlistContextType,
  PendingGroupLayby,
  UserGroup,
} from "@/types/wishlist";

const WishlistContext = createContext<WishlistContextType | undefined>(
  undefined
);

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [pendingGroupLaybys, setPendingGroupLaybys] = useState<
    PendingGroupLayby[]
  >([]);
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedWishlist = localStorage.getItem("khenesis-wishlist");
    if (savedWishlist) {
      try {
        const parsed = JSON.parse(savedWishlist);
        // Convert date strings back to Date objects
        const items = parsed.map((item: any) => ({
          ...item,
          addedAt: new Date(item.addedAt),
        }));
        setWishlistItems(items);
      } catch (error) {
        console.error("Failed to parse wishlist from localStorage:", error);
      }
    }

    const savedGroupLaybys = localStorage.getItem(
      "khenesis-pending-group-laybys"
    );
    if (savedGroupLaybys) {
      try {
        const parsed = JSON.parse(savedGroupLaybys);
        const items = parsed.map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt),
        }));
        setPendingGroupLaybys(items);
      } catch (error) {
        console.error("Failed to parse group laybys from localStorage:", error);
      }
    }

    // Mock user groups for now - in real app this would come from API
    setUserGroups([
      {
        id: "group-1",
        name: "Family Memorial",
        memberCount: 4,
        isActive: true,
        stage: "discussion",
      },
      {
        id: "group-2",
        name: "Friends Tribute",
        memberCount: 6,
        isActive: true,
        stage: "suggestion",
      },
    ]);
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("khenesis-wishlist", JSON.stringify(wishlistItems));
  }, [wishlistItems]);

  useEffect(() => {
    localStorage.setItem(
      "khenesis-pending-group-laybys",
      JSON.stringify(pendingGroupLaybys)
    );
  }, [pendingGroupLaybys]);

  const addToWishlist = (productId: string) => {
    setWishlistItems((prev) => {
      // Check if item already exists
      if (prev.some((item) => item.productId === productId)) {
        return prev;
      }
      return [...prev, { productId, addedAt: new Date() }];
    });
  };

  const removeFromWishlist = (productId: string) => {
    setWishlistItems((prev) =>
      prev.filter((item) => item.productId !== productId)
    );
  };

  const isInWishlist = (productId: string) => {
    return wishlistItems.some((item) => item.productId === productId);
  };

  const wishlistCount = wishlistItems.length;

  const createGroupLayby = async (
    productId: string,
    termMonths: number,
    targetGroupSize: number
  ): Promise<string> => {
    const newGroupLayby: PendingGroupLayby = {
      id: `group-layby-${Date.now()}`,
      productId,
      termMonths,
      targetGroupSize,
      createdAt: new Date(),
      invitesSent: [],
      status: "pending",
    };

    setPendingGroupLaybys((prev) => [...prev, newGroupLayby]);
    return newGroupLayby.id;
  };

  const addToExistingGroup = async (
    productId: string,
    groupId: string
  ): Promise<void> => {
    // In a real app, this would make an API call to add the product to the group
    console.log(`Adding product ${productId} to group ${groupId}`);
    // For now, we'll just simulate success
    return Promise.resolve();
  };

  const getPendingGroupLaybys = (): PendingGroupLayby[] => {
    return pendingGroupLaybys.filter((layby) => layby.status === "pending");
  };

  const getActiveGroups = (): UserGroup[] => {
    return userGroups.filter(
      (group) => group.isActive && group.stage !== "shipping"
    );
  };

  const value: WishlistContextType = {
    wishlistItems,
    pendingGroupLaybys,
    userGroups,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    wishlistCount,
    createGroupLayby,
    addToExistingGroup,
    getPendingGroupLaybys,
    getActiveGroups,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error("useWishlist must be used within a WishlistProvider");
  }
  return context;
}

"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useWishlist } from "@/contexts/wishlist-context";
import { formatPrice } from "@/lib/utils";
import { products } from "@/data/products";
import { useRouter } from "next/navigation";
import { Users, Calendar, DollarSign, Check, Loader2 } from "lucide-react";

interface GroupLaybyCreationFlowProps {
  productId: string;
  onComplete: () => void;
  onCancel: () => void;
}

export function GroupLaybyCreationFlow({
  productId,
  onComplete,
  onCancel,
}: GroupLaybyCreationFlowProps) {
  const [termMonths, setTermMonths] = useState([12]);
  const [groupSize, setGroupSize] = useState([4]);
  const [isCreating, setIsCreating] = useState(false);
  const { createGroupLayby } = useWishlist();
  const router = useRouter();

  // Find the product
  const product = products.find((p) => p.id === productId);
  if (!product) return null;

  const selectedTerm = termMonths[0];
  const selectedGroupSize = groupSize[0];
  const totalPrice = product.price;
  const monthlyPayment = totalPrice / selectedTerm;
  const individualShare = monthlyPayment / selectedGroupSize;

  const handleCreateGroup = async () => {
    setIsCreating(true);
    try {
      // Store the group layby preferences and redirect to groups/new
      const groupId = await createGroupLayby(
        productId,
        selectedTerm,
        selectedGroupSize
      );
      console.log(`Created group layby: ${groupId}`);

      // Redirect to groups/new with the product and preferences
      const params = new URLSearchParams({
        productId: productId,
        termMonths: selectedTerm.toString(),
        groupSize: selectedGroupSize.toString(),
        laybyId: groupId,
      });

      router.push(`/groups/new?${params.toString()}`);
      onComplete();
    } catch (error) {
      console.error("Failed to create group layby:", error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Product Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{product.name}</h3>
              <p className="text-sm text-muted-foreground">
                Total: {formatPrice(totalPrice)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Term Selection */}
      <div className="space-y-3">
        <Label className="text-base font-medium flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          Payment Term
        </Label>
        <div className="space-y-4">
          <Slider
            value={termMonths}
            onValueChange={setTermMonths}
            max={36}
            min={3}
            step={3}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>3 months</span>
            <span className="font-medium text-foreground">
              {selectedTerm} months
            </span>
            <span>36 months</span>
          </div>
          <div className="text-center">
            <Badge variant="outline" className="text-sm">
              {formatPrice(monthlyPayment)}/month total
            </Badge>
          </div>
        </div>
      </div>

      {/* Group Size Selection */}
      <div className="space-y-3">
        <Label className="text-base font-medium flex items-center gap-2">
          <Users className="h-4 w-4" />
          Group Size
        </Label>
        <div className="space-y-4">
          <Slider
            value={groupSize}
            onValueChange={setGroupSize}
            max={10}
            min={2}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>2 people</span>
            <span className="font-medium text-foreground">
              {selectedGroupSize} people
            </span>
            <span>10 people</span>
          </div>
          <div className="text-center">
            <Badge variant="secondary" className="text-sm">
              {formatPrice(individualShare)}/month per person
            </Badge>
          </div>
        </div>
      </div>

      {/* Summary */}
      <Card className="bg-primary/5 border-primary/20">
        <CardContent className="p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Check className="h-4 w-4 text-primary" />
            Group Layby Summary
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Product:</span>
              <span className="font-medium">{product.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Price:</span>
              <span className="font-medium">{formatPrice(totalPrice)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Payment Term:</span>
              <span className="font-medium">{selectedTerm} months</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Group Size:</span>
              <span className="font-medium">{selectedGroupSize} people</span>
            </div>
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  Your monthly payment:
                </span>
                <span className="font-bold text-primary">
                  {formatPrice(individualShare)}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4">
        <Button
          variant="outline"
          onClick={onCancel}
          className="flex-1"
          disabled={isCreating}
        >
          Cancel
        </Button>
        <Button
          onClick={handleCreateGroup}
          className="flex-1"
          disabled={isCreating}
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Users className="mr-2 h-4 w-4" />
              Create Group
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

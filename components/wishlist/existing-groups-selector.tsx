"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useWishlist } from "@/contexts/wishlist-context";
import { UserGroup } from "@/types/wishlist";
import { 
  Users, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Loader2,
  ChevronRight
} from "lucide-react";

interface ExistingGroupsSelectorProps {
  productId: string;
  groups: UserGroup[];
  onComplete: () => void;
  onCancel: () => void;
}

export function ExistingGroupsSelector({ 
  productId, 
  groups, 
  onComplete, 
  onCancel 
}: ExistingGroupsSelectorProps) {
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const { addToExistingGroup } = useWishlist();

  const handleAddToGroup = async () => {
    if (!selectedGroupId) return;
    
    setIsAdding(true);
    try {
      await addToExistingGroup(productId, selectedGroupId);
      onComplete();
    } catch (error) {
      console.error('Failed to add to group:', error);
    } finally {
      setIsAdding(false);
    }
  };

  const getStageIcon = (stage: UserGroup['stage']) => {
    switch (stage) {
      case 'suggestion':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'discussion':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'payment':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'manufacturing':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStageLabel = (stage: UserGroup['stage']) => {
    switch (stage) {
      case 'suggestion':
        return 'Suggesting';
      case 'discussion':
        return 'Discussing';
      case 'payment':
        return 'Payment';
      case 'manufacturing':
        return 'Manufacturing';
      default:
        return stage;
    }
  };

  const getStageColor = (stage: UserGroup['stage']) => {
    switch (stage) {
      case 'suggestion':
        return 'bg-yellow-100 text-yellow-800';
      case 'discussion':
        return 'bg-blue-100 text-blue-800';
      case 'payment':
        return 'bg-green-100 text-green-800';
      case 'manufacturing':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (groups.length === 0) {
    return (
      <div className="text-center py-8">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2">No Active Groups</h3>
        <p className="text-muted-foreground mb-4">
          You don't have any active groups to add this product to.
        </p>
        <Button onClick={onCancel} variant="outline">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {groups.map((group) => (
          <Card 
            key={group.id}
            className={`cursor-pointer transition-all ${
              selectedGroupId === group.id 
                ? 'ring-2 ring-primary bg-primary/5' 
                : 'hover:bg-accent/50'
            }`}
            onClick={() => setSelectedGroupId(group.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium truncate">{group.name}</h3>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getStageColor(group.stage)}`}
                    >
                      <div className="flex items-center gap-1">
                        {getStageIcon(group.stage)}
                        {getStageLabel(group.stage)}
                      </div>
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {group.memberCount} {group.memberCount === 1 ? 'member' : 'members'}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  {selectedGroupId === group.id ? (
                    <CheckCircle className="h-5 w-5 text-primary" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-muted-foreground" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4">
        <Button 
          variant="outline" 
          onClick={onCancel}
          className="flex-1"
          disabled={isAdding}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleAddToGroup}
          className="flex-1"
          disabled={!selectedGroupId || isAdding}
        >
          {isAdding ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Users className="mr-2 h-4 w-4" />
              Add to Group
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

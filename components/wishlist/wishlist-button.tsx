"use client";

import { <PERSON>, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useWishlist } from "@/contexts/wishlist-context";
import { cn } from "@/lib/utils";
import { WishlistActionSheet } from "./wishlist-action-sheet";

interface WishlistButtonProps {
  productId: string;
  variant?: "default" | "ghost" | "outline" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showText?: boolean;
  showGroupIcon?: boolean;
  useActionSheet?: boolean;
}

export function WishlistButton({
  productId,
  variant = "ghost",
  size = "icon",
  className,
  showText = false,
  showGroupIcon = false,
  useActionSheet = true,
}: WishlistButtonProps) {
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist();
  const isLiked = isInWishlist(productId);

  const handleDirectClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation when used inside links
    e.stopPropagation();

    if (isLiked) {
      removeFromWishlist(productId);
    } else {
      addToWishlist(productId);
    }
  };

  const buttonContent = (
    <Button
      variant={variant}
      size={size}
      onClick={useActionSheet ? undefined : handleDirectClick}
      className={cn(
        "transition-colors relative",
        isLiked && "text-red-500 hover:text-red-600",
        className
      )}
      aria-label={isLiked ? "Remove from wishlist" : "Add to wishlist"}
    >
      <div className="flex items-center">
        <Heart
          className={cn(
            "h-4 w-4",
            size === "icon" && "h-5 w-5",
            isLiked && "fill-current"
          )}
        />
        {showGroupIcon && <Users className="h-3 w-3 ml-1 opacity-60" />}
      </div>
      {showText && (
        <span className="ml-2">
          {isLiked ? "Remove from wishlist" : "Add to wishlist"}
        </span>
      )}
    </Button>
  );

  if (useActionSheet) {
    return (
      <WishlistActionSheet productId={productId}>
        <div
          title={
            showGroupIcon
              ? "Save to wishlist or start a group layby"
              : undefined
          }
        >
          {buttonContent}
        </div>
      </WishlistActionSheet>
    );
  }

  return buttonContent;
}

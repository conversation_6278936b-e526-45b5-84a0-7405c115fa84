"use client";

import { useState } from "react";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  SheetDes<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useWishlist } from "@/contexts/wishlist-context";
import { 
  Heart, 
  Users, 
  UserPlus, 
  Calendar,
  ChevronRight,
  ArrowLeft
} from "lucide-react";
import { GroupLaybyCreationFlow } from "./group-layby-creation-flow";
import { ExistingGroupsSelector } from "./existing-groups-selector";

interface WishlistActionSheetProps {
  productId: string;
  children: React.ReactNode;
  onClose?: () => void;
}

type ActionSheetView = 'main' | 'create-group' | 'existing-groups';

export function WishlistActionSheet({ 
  productId, 
  children, 
  onClose 
}: WishlistActionSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentView, setCurrentView] = useState<ActionSheetView>('main');
  const { 
    isInWishlist, 
    addToWishlist, 
    removeFromWishlist,
    getActiveGroups 
  } = useWishlist();

  const isLiked = isInWishlist(productId);
  const activeGroups = getActiveGroups();

  const handleClose = () => {
    setIsOpen(false);
    setCurrentView('main');
    onClose?.();
  };

  const handleAddToWishlist = () => {
    if (isLiked) {
      removeFromWishlist(productId);
    } else {
      addToWishlist(productId);
    }
    handleClose();
  };

  const handleBackToMain = () => {
    setCurrentView('main');
  };

  const renderMainView = () => (
    <>
      <SheetHeader>
        <SheetTitle>What would you like to do with this item?</SheetTitle>
        <SheetDescription>
          Choose how you'd like to save or purchase this product
        </SheetDescription>
      </SheetHeader>

      <div className="space-y-3 mt-6">
        {/* Add to Personal Wishlist */}
        <Button
          variant="outline"
          className="w-full justify-start h-auto p-4"
          onClick={handleAddToWishlist}
        >
          <div className="flex items-center gap-3 w-full">
            <div className="flex-shrink-0">
              <Heart className={`h-5 w-5 ${isLiked ? 'fill-red-500 text-red-500' : ''}`} />
            </div>
            <div className="flex-1 text-left">
              <div className="font-medium">
                {isLiked ? 'Remove from' : 'Add to'} Personal Wishlist
              </div>
              <div className="text-sm text-muted-foreground">
                {isLiked ? 'Remove this item from your saved items' : 'Save this item for later'}
              </div>
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </Button>

        <Separator />

        {/* Start a New Group Layby */}
        <Button
          variant="outline"
          className="w-full justify-start h-auto p-4"
          onClick={() => setCurrentView('create-group')}
        >
          <div className="flex items-center gap-3 w-full">
            <div className="flex-shrink-0">
              <UserPlus className="h-5 w-5" />
            </div>
            <div className="flex-1 text-left">
              <div className="font-medium">Start a New Group Layby</div>
              <div className="text-sm text-muted-foreground">
                Pick term + group size, then invite others
              </div>
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </Button>

        {/* Add to Existing Group */}
        {activeGroups.length > 0 && (
          <Button
            variant="outline"
            className="w-full justify-start h-auto p-4"
            onClick={() => setCurrentView('existing-groups')}
          >
            <div className="flex items-center gap-3 w-full">
              <div className="flex-shrink-0">
                <Users className="h-5 w-5" />
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">Add to Existing Group</div>
                <div className="text-sm text-muted-foreground">
                  Add to one of your {activeGroups.length} active groups
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {activeGroups.length}
                </Badge>
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </Button>
        )}
      </div>
    </>
  );

  const renderCreateGroupView = () => (
    <>
      <SheetHeader>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBackToMain}
            className="h-8 w-8"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <SheetTitle>Start New Group Layby</SheetTitle>
            <SheetDescription>
              Set up payment terms and group size
            </SheetDescription>
          </div>
        </div>
      </SheetHeader>

      <div className="mt-6">
        <GroupLaybyCreationFlow 
          productId={productId} 
          onComplete={handleClose}
          onCancel={handleBackToMain}
        />
      </div>
    </>
  );

  const renderExistingGroupsView = () => (
    <>
      <SheetHeader>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBackToMain}
            className="h-8 w-8"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <SheetTitle>Add to Existing Group</SheetTitle>
            <SheetDescription>
              Choose which group to add this product to
            </SheetDescription>
          </div>
        </div>
      </SheetHeader>

      <div className="mt-6">
        <ExistingGroupsSelector 
          productId={productId}
          groups={activeGroups}
          onComplete={handleClose}
          onCancel={handleBackToMain}
        />
      </div>
    </>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create-group':
        return renderCreateGroupView();
      case 'existing-groups':
        return renderExistingGroupsView();
      default:
        return renderMainView();
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild onClick={() => setIsOpen(true)}>
        {children}
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[85vh] px-0 sm:max-w-md sm:rounded-t-xl">
        <div className="px-4 h-full flex flex-col">
          {renderCurrentView()}
        </div>
      </SheetContent>
    </Sheet>
  );
}

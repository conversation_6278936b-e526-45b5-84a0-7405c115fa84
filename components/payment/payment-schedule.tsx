"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatPrice } from "@/lib/utils";
import {
  CreditCard,
  Clock,
  CheckCircle,
  Circle,
  ShoppingBag,
} from "lucide-react";
import Image from "next/image";

interface PaymentScheduleItem {
  id: number;
  dueDate: string;
  amount: number;
  status: "paid" | "upcoming" | "overdue";
  paymentNumber: number;
  totalPayments: number;
  isFinalPayment?: boolean;
}

interface PaymentScheduleProps {
  productName: string;
  productCode: string;
  productImage?: string;
  totalAmount: number;
  amountPaid: number;
  schedule: PaymentScheduleItem[];
  paymentMethod?: {
    type: string;
    last4: string;
  };
  onMakePayment?: () => void;
}

export function PaymentSchedule({
  productName,
  productCode,
  productImage,
  totalAmount,
  amountPaid,
  schedule,
  paymentMethod,
  onMakePayment,
}: PaymentScheduleProps) {
  const remainingAmount = totalAmount - amountPaid;
  const nextPayment = schedule.find((item) => item.status === "upcoming");

  const getStatusIcon = (status: PaymentScheduleItem["status"]) => {
    switch (status) {
      case "paid":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "upcoming":
        return <Circle className="h-4 w-4 text-blue-600" />;
      case "overdue":
        return <Circle className="h-4 w-4 text-red-600" />;
      default:
        return <Circle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: PaymentScheduleItem["status"]) => {
    switch (status) {
      case "paid":
        return "text-green-600";
      case "upcoming":
        return "text-blue-600";
      case "overdue":
        return "text-red-600";
      default:
        return "text-muted-foreground";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-4">
      {/* Header with product info - using manufacturing tab design */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-muted rounded-lg overflow-hidden flex items-center justify-center flex-shrink-0">
              {productImage ? (
                <div className="relative w-full h-full">
                  <Image
                    src={productImage}
                    alt={productName}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <ShoppingBag className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{productCode}</h3>
              <p className="text-sm text-muted-foreground">{productName}</p>
              <p className="text-sm text-muted-foreground">
                {formatPrice(totalAmount)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment summary */}
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Paid</div>
              <div className="text-2xl font-bold">
                {formatPrice(amountPaid)}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                {schedule.filter((s) => s.status !== "paid").length} Remaining
              </div>
              <div className="text-2xl font-bold">
                {formatPrice(remainingAmount)}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Next payment highlight */}
      {nextPayment && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-sm font-medium text-primary">
                  Next payment
                </div>
                <div className="text-lg font-bold">
                  {formatDate(nextPayment.dueDate)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {nextPayment.paymentNumber} of {nextPayment.totalPayments}
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {formatPrice(nextPayment.amount)}
                </div>
                <Badge
                  variant="secondary"
                  className="bg-primary/10 text-primary"
                >
                  {nextPayment.totalPayments} months
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Payment schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {schedule.map((payment, index) => (
            <div key={payment.id} className="flex items-center gap-3">
              {/* Timeline indicator */}
              <div className="flex flex-col items-center">
                {getStatusIcon(payment.status)}
                {index < schedule.length - 1 && (
                  <div className="w-px h-8 bg-border mt-2" />
                )}
              </div>

              {/* Payment details */}
              <div className="flex-1 flex items-center justify-between">
                <div>
                  <div
                    className={`font-medium ${getStatusColor(payment.status)}`}
                  >
                    {payment.status === "upcoming" ? "Next payment: " : ""}
                    {formatDate(payment.dueDate)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {payment.paymentNumber} of {payment.totalPayments}
                    {payment.isFinalPayment && " • Final payment"}
                  </div>
                </div>
                <div
                  className={`font-medium ${getStatusColor(payment.status)}`}
                >
                  {formatPrice(payment.amount)}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Payment method */}
      {paymentMethod && (
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded">
                  <span className="text-blue-600 font-bold text-sm">VISA</span>
                </div>
                <div>
                  <div className="font-medium">•••• {paymentMethod.last4}</div>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Change
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Make payment button */}
      {nextPayment && onMakePayment && (
        <Button onClick={onMakePayment} className="w-full" size="lg">
          <CreditCard className="h-4 w-4 mr-2" />
          Make a payment
        </Button>
      )}
    </div>
  );
}

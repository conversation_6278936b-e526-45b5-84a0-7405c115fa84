"use client";

import { useState, useEffect } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ChevronRight,
  Plus,
  User,
  X,
  Calendar,
  Users,
  DollarSign,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { products } from "@/data/products";
import { formatPrice } from "@/lib/utils";

export default function NewGroupPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [step, setStep] = useState(1);
  const [groupName, setGroupName] = useState("");
  const [invites, setInvites] = useState<string[]>([]);
  const [email, setEmail] = useState("");
  const [productId, setProductId] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [termMonths, setTermMonths] = useState<number | null>(null);
  const [groupSize, setGroupSize] = useState<number | null>(null);
  const [laybyId, setLaybyId] = useState<string | null>(null);

  // Get parameters from URL if available (from wishlist action sheet)
  useEffect(() => {
    const pid = searchParams.get("productId");
    const qty = searchParams.get("quantity");
    const term = searchParams.get("termMonths");
    const size = searchParams.get("groupSize");
    const lId = searchParams.get("laybyId");

    if (pid) setProductId(pid);
    if (qty) setQuantity(parseInt(qty, 10) || 1);
    if (term) setTermMonths(parseInt(term, 10));
    if (size) setGroupSize(parseInt(size, 10));
    if (lId) setLaybyId(lId);

    // Auto-generate group name if coming from wishlist
    if (pid && !groupName) {
      const product = products.find((p) => p.id === pid);
      if (product) {
        setGroupName(`${product.name} Group`);
      }
    }
  }, [searchParams, groupName]);

  const handleAddInvite = () => {
    if (email && !invites.includes(email)) {
      setInvites([...invites, email]);
      setEmail("");
    }
  };

  const handleRemoveInvite = (emailToRemove: string) => {
    setInvites(invites.filter((e) => e !== emailToRemove));
  };

  const handleCreateGroup = () => {
    // In a real app, this would send data to the server
    console.log("Creating group:", {
      name: groupName,
      productId,
      quantity,
      invites,
    });

    // Navigate to the new group page (mock ID for now)
    router.push("/groups/new-group-123");
  };

  return (
    <MobileLayout>
      <div className="pb-24">
        <div className="bg-primary text-primary-foreground p-4">
          <h1 className="text-2xl font-bold">Create New Group</h1>
          <p className="text-primary-foreground/80 mt-1">
            Invite friends to purchase together
          </p>
        </div>

        <div className="px-4 py-6">
          {step === 1 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Group Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label
                      htmlFor="groupName"
                      className="block text-sm font-medium mb-1"
                    >
                      Group Name
                    </label>
                    <Input
                      id="groupName"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      placeholder="Enter a name for your group"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Choose a name that helps identify your group purpose
                    </p>
                  </div>

                  {productId ? (
                    <div className="border rounded-md p-3">
                      <div className="text-sm font-medium mb-3">
                        Selected Product
                      </div>
                      {(() => {
                        const product = products.find(
                          (p) => p.id === productId
                        );
                        if (!product) return null;

                        const monthlyPayment = termMonths
                          ? product.price / termMonths
                          : 0;
                        const individualShare =
                          groupSize && termMonths
                            ? monthlyPayment / groupSize
                            : 0;

                        return (
                          <div className="space-y-3">
                            <div className="flex items-center">
                              <div className="h-16 w-16 relative rounded overflow-hidden">
                                <Image
                                  src={
                                    product.images[0] ||
                                    "/images/placeholder.png"
                                  }
                                  alt={product.name}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div className="ml-3 flex-1">
                                <div className="font-medium">
                                  {product.name}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {formatPrice(product.price)} • Quantity:{" "}
                                  {quantity}
                                </div>
                              </div>
                            </div>

                            {termMonths && groupSize && (
                              <div className="bg-primary/5 rounded-md p-3 space-y-2">
                                <div className="flex items-center gap-2 text-sm">
                                  <Calendar className="h-4 w-4 text-primary" />
                                  <span className="font-medium">
                                    Layby Terms:
                                  </span>
                                  <Badge variant="outline">
                                    {termMonths} months
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <Users className="h-4 w-4 text-primary" />
                                  <span className="font-medium">
                                    Group Size:
                                  </span>
                                  <Badge variant="outline">
                                    {groupSize} people
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <DollarSign className="h-4 w-4 text-primary" />
                                  <span className="font-medium">
                                    Your monthly payment:
                                  </span>
                                  <Badge
                                    variant="secondary"
                                    className="font-bold"
                                  >
                                    {formatPrice(individualShare)}/month
                                  </Badge>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  ) : (
                    <div className="border rounded-md p-3 text-center">
                      <p className="text-sm text-muted-foreground">
                        No product selected. You can select a product later.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Button
                className="w-full"
                onClick={() => setStep(2)}
                disabled={!groupName}
              >
                Continue to Invites
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Invite Members</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium mb-1"
                    >
                      Email Address
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter email address"
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        onClick={handleAddInvite}
                        disabled={!email || invites.includes(email)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {invites.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium mb-2">
                        Invited Members
                      </h3>
                      <div className="space-y-2">
                        {invites.map((inviteEmail) => (
                          <div
                            key={inviteEmail}
                            className="flex items-center justify-between border rounded-md p-2"
                          >
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm">{inviteEmail}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveInvite(inviteEmail)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2 pt-2">
                    <input
                      type="checkbox"
                      id="allowJoin"
                      className="h-4 w-4 border border-gray-300 rounded"
                    />
                    <label
                      htmlFor="allowJoin"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Allow others to join with a link
                    </label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button className="flex-1" onClick={handleCreateGroup}>
                  Create Group
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </MobileLayout>
  );
}

"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { ProductCard } from "@/components/product/product-card";
import { useWishlist } from "@/contexts/wishlist-context";
import { products } from "@/data/products";
import { Heart, ShoppingBag, Users, UserPlus } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { WishlistActionSheet } from "@/components/wishlist/wishlist-action-sheet";
import { formatPrice } from "@/lib/utils";

export default function WishlistPage() {
  const { wishlistItems } = useWishlist();

  // Get products that are in the wishlist
  const wishlistProducts = products.filter((product) =>
    wishlistItems.some((item) => item.productId === product.id)
  );

  if (wishlistItems.length === 0) {
    return (
      <MobileLayout>
        <div className="container py-4">
          <h1 className="text-2xl font-bold mb-6">My Wishlist</h1>

          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
              <Heart className="h-8 w-8 text-muted-foreground" />
            </div>
            <h2 className="text-lg font-semibold mb-2">
              Your wishlist is empty
            </h2>
            <p className="text-muted-foreground mb-6 max-w-sm">
              Start adding products you love to your wishlist by tapping the
              heart icon on any product.
            </p>
            <Link href="/products">
              <Button>
                <ShoppingBag className="mr-2 h-4 w-4" />
                Browse Products
              </Button>
            </Link>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout>
      <div className="container py-4">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">My Wishlist</h1>
          <span className="text-sm text-muted-foreground">
            {wishlistItems.length}{" "}
            {wishlistItems.length === 1 ? "item" : "items"}
          </span>
        </div>

        <div className="space-y-4">
          {wishlistProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="flex">
                  {/* Product Image */}
                  <div className="w-24 h-24 relative flex-shrink-0">
                    <img
                      src={product.images[0] || "/images/placeholder.png"}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Product Details */}
                  <div className="flex-1 p-4">
                    <Link href={`/products/${product.id}`}>
                      <h3 className="font-medium text-sm hover:text-primary transition-colors line-clamp-1">
                        {product.name}
                      </h3>
                    </Link>
                    <p className="text-xs text-muted-foreground mt-1">
                      by {product.merchant.name}
                    </p>
                    <p className="text-sm font-semibold mt-2">
                      {formatPrice(product.price)}
                    </p>
                  </div>
                </div>

                {/* Group Actions */}
                <div className="px-4 pb-4 space-y-2">
                  <div className="flex gap-2">
                    <Link href={`/products/${product.id}`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        View Details
                      </Button>
                    </Link>
                    <WishlistActionSheet productId={product.id}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-shrink-0"
                      >
                        <Users className="mr-2 h-3 w-3" />
                        Group Buy
                      </Button>
                    </WishlistActionSheet>
                  </div>

                  <div className="flex gap-2">
                    <WishlistActionSheet productId={product.id}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <UserPlus className="mr-2 h-3 w-3" />
                        Invite Friends to Group Buy
                      </Button>
                    </WishlistActionSheet>
                    <WishlistActionSheet productId={product.id}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-xs"
                      >
                        <Users className="mr-2 h-3 w-3" />
                        Convert to Group Layby
                      </Button>
                    </WishlistActionSheet>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </MobileLayout>
  );
}

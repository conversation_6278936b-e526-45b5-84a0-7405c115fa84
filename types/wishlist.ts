export interface WishlistItem {
  productId: string;
  addedAt: Date;
}

export interface PendingGroupLayby {
  id: string;
  productId: string;
  termMonths: number;
  targetGroupSize: number;
  createdAt: Date;
  invitesSent: string[];
  status: "pending" | "active" | "cancelled";
}

export interface UserGroup {
  id: string;
  name: string;
  memberCount: number;
  isActive: boolean;
  stage: "suggestion" | "discussion" | "payment" | "manufacturing" | "shipping";
}

export interface WishlistContextType {
  wishlistItems: WishlistItem[];
  pendingGroupLaybys: PendingGroupLayby[];
  userGroups: UserGroup[];
  addToWishlist: (productId: string) => void;
  removeFromWishlist: (productId: string) => void;
  isInWishlist: (productId: string) => boolean;
  wishlistCount: number;
  createGroupLayby: (
    productId: string,
    termMonths: number,
    targetGroupSize: number
  ) => Promise<string>;
  addToExistingGroup: (productId: string, groupId: string) => Promise<void>;
  getPendingGroupLaybys: () => PendingGroupLayby[];
  getActiveGroups: () => UserGroup[];
}
